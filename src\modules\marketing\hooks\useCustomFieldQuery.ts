/**
 * Hooks for audience custom field API using TanStack Query
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { CustomFieldService } from '../services/custom-field.service';
import {
  CreateCustomFieldRequest,
  CustomFieldQueryParams,
  UpdateCustomFieldRequest,
} from '../types/custom-field.types';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';

/**
 * Query keys for custom field API
 */
export const CUSTOM_FIELD_QUERY_KEYS = {
  all: ['marketing', 'custom-fields'] as const,
  list: (params: CustomFieldQueryParams) =>
    [...CUSTOM_FIELD_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: number) => [...CUSTOM_FIELD_QUERY_KEYS.all, 'detail', id] as const,
};

/**
 * Hook to get custom fields with pagination and filtering
 */
export const useCustomFields = (params: CustomFieldQueryParams = {}) => {
  return useQuery({
    queryKey: CUSTOM_FIELD_QUERY_KEYS.list(params),
    queryFn: async () => {
      console.log('Calling API with params:', params);
      try {
        const response = await CustomFieldService.getCustomFields(params);
        console.log('Raw API response:', response);
        return response;
      } catch (error) {
        console.error('Error fetching custom fields:', error);
        throw error;
      }
    },
    select: data => {
      console.log('Selecting data from response:', data);
      // Ensure we have a standardized response structure
      if (data && data.result) {
        // If the API returns a standard structure with result
        return data.result;
      } else if (data) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const anyData = data as any;

        // Nếu API trả về cấu trúc { data: [...], meta: {...} }
        if (anyData.data && Array.isArray(anyData.data)) {
          return {
            items: anyData.data,
            meta: anyData.meta || {
              page: 1,
              limit: 10,
              total: anyData.data.length,
              totalPages: Math.ceil(anyData.data.length / 10)
            }
          };
        }
        // Nếu API trả về cấu trúc { items: [...], meta: {...} }
        else if (anyData.items && Array.isArray(anyData.items)) {
          return anyData;
        }
        // Nếu API trả về mảng trực tiếp
        else if (Array.isArray(anyData)) {
          return {
            items: anyData,
            meta: {
              page: 1,
              limit: 10,
              total: anyData.length,
              totalPages: Math.ceil(anyData.length / 10)
            }
          };
        }
        // If the API returns a non-standard structure, try to adapt it
        else {
          console.log('Non-standard API response, attempting to adapt');
          return anyData;
        }
      }
      // Return empty data if nothing is available
      return { items: [], meta: { page: 1, total: 0, limit: 10, totalPages: 1 } };
    },
    // Cấu hình query để tránh load liên tục
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
    refetchOnMount: false,
    refetchInterval: false,
    retry: 1
  });
};

/**
 * Hook to get custom field by ID
 */
export const useCustomField = (id: number) => {
  return useQuery({
    queryKey: CUSTOM_FIELD_QUERY_KEYS.detail(id),
    queryFn: () => CustomFieldService.getCustomFieldById(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook to create custom field
 */
export const useCreateCustomField = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCustomFieldRequest) => CustomFieldService.createCustomField(data),
    onSuccess: (data) => {
      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: data.message || 'Thêm trường tùy chỉnh thành công'
      });

      // Chỉ invalidate query list để tránh re-render không cần thiết
      queryClient.invalidateQueries({
        queryKey: CUSTOM_FIELD_QUERY_KEYS.all,
        exact: false,
        refetchType: 'active'
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      // Hiển thị thông báo lỗi
      NotificationUtil.error({
        message: error.response?.data?.message || 'Thêm trường tùy chỉnh thất bại'
      });
    },
  });
};

/**
 * Hook to update custom field
 */
export const useUpdateCustomField = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateCustomFieldRequest }) =>
      CustomFieldService.updateCustomField(id, data),
    onSuccess: (data, variables) => {
      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: data.message || 'Cập nhật trường tùy chỉnh thành công'
      });

      // Invalidate queries để refresh dữ liệu
      queryClient.invalidateQueries({ queryKey: CUSTOM_FIELD_QUERY_KEYS.detail(variables.id) });
      queryClient.invalidateQueries({
        queryKey: CUSTOM_FIELD_QUERY_KEYS.all,
        exact: false,
        refetchType: 'active'
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      // Hiển thị thông báo lỗi
      NotificationUtil.error({
        message: error.response?.data?.message || 'Cập nhật trường tùy chỉnh thất bại'
      });
    },
  });
};

/**
 * Hook to delete custom field
 */
export const useDeleteCustomField = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => CustomFieldService.deleteCustomField(id),
    onSuccess: (data) => {
      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: data.message || 'Xóa trường tùy chỉnh thành công'
      });

      // Chỉ invalidate query list để tránh re-render không cần thiết
      queryClient.invalidateQueries({
        queryKey: CUSTOM_FIELD_QUERY_KEYS.all,
        exact: false,
        refetchType: 'active'
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      // Hiển thị thông báo lỗi
      NotificationUtil.error({
        message: error.response?.data?.message || 'Xóa trường tùy chỉnh thất bại'
      });
    },
  });
};

/**
 * Hook to delete multiple custom fields
 */
export const useDeleteMultipleCustomFields = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (customFieldIds: number[]) => CustomFieldService.deleteMultipleCustomFields(customFieldIds),
    onSuccess: (data, customFieldIds) => {
      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: data.message || `Xóa ${customFieldIds.length} trường tùy chỉnh thành công`
      });

      // Chỉ invalidate query list để tránh re-render không cần thiết
      queryClient.invalidateQueries({
        queryKey: CUSTOM_FIELD_QUERY_KEYS.all,
        exact: false,
        refetchType: 'active'
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      // Hiển thị thông báo lỗi
      NotificationUtil.error({
        message: error.response?.data?.message || 'Xóa trường tùy chỉnh thất bại'
      });
    },
  });
};
