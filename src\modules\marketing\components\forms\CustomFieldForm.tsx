import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Select,
  Card,
  Textarea,
  ConditionalField,
  DatePicker,
  Checkbox,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { z } from 'zod';
import { useCreateCustomField, useUpdateCustomField } from '../../hooks/useCustomFieldQuery';
import { CustomFieldDetail } from '../../services/custom-field.service';
import { CreateCustomFieldRequest, UpdateCustomFieldRequest, CustomFieldType } from '../../types/custom-field.types';
import { ConditionType } from '@/shared/hooks/useFieldCondition';

interface CustomFieldFormProps {
  onSubmit: () => void;
  onCancel: () => void;
  initialData?: CustomFieldDetail;
}

/**
 * Form tạo và chỉnh sửa trường tùy chỉnh
 */
const CustomFieldForm: React.FC<CustomFieldFormProps> = ({
  onSubmit,
  initialData,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const { mutateAsync: createCustomField } = useCreateCustomField();
  const { mutateAsync: updateCustomField } = useUpdateCustomField();

  const formRef = useRef<FormRef<Record<string, unknown>> | null>(null);

  // State cho advanced settings
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  // Schema cho form
  const customFieldSchema = z.object({
    id: z.string().min(1, t('marketing:customField.form.idRequired')),
    displayName: z.string().min(1, t('marketing:customField.form.displayNameRequired')),
    label: z.string().optional(), // Nhãn không bắt buộc
    type: z.string().min(1, t('marketing:customField.form.typeRequired')),
    placeholder: z.string().optional(),
    defaultValue: z.string().optional(),
    description: z.string().optional(),
    validation: z.object({
      minLength: z.string().optional(),
      maxLength: z.string().optional(),
      pattern: z.string().optional(),
    }).optional(),
    options: z.string().optional(),
  });

  // Giá trị mặc định cho form
  const defaultValues = {
    id: '',
    displayName: '',
    type: 'text',
    placeholder: '',
    defaultValue: '',
    description: '',
    validation: {
      minLength: '',
      maxLength: '',
      pattern: '',
    },
    options: '',
  };

  // Định nghĩa kiểu dữ liệu cho form values
  type CustomFieldFormValues = z.infer<typeof customFieldSchema>;

  // Xử lý khi submit form
  const handleSubmit = async (values: CustomFieldFormValues) => {
    try {
      if (initialData) {
        // Cập nhật trường tùy chỉnh - sử dụng UpdateCustomFieldRequest
        const updateData: UpdateCustomFieldRequest = {
          fieldKey: String(values.id),
          displayName: String(values.displayName),
          dataType: String(values.type) as CustomFieldType,
          description: values.description ? String(values.description) : undefined,
        };

        await updateCustomField({
          id: initialData.id,
          data: updateData,
        });
      } else {
        // Tạo trường tùy chỉnh mới - sử dụng CreateCustomFieldRequest
        const createData: CreateCustomFieldRequest = {
          fieldKey: String(values.id),
          displayName: String(values.displayName),
          dataType: String(values.type) as CustomFieldType,
          description: values.description ? String(values.description) : undefined,
        };

        await createCustomField(createData);
      }

      // Gọi callback onSubmit
      onSubmit();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  // Chuẩn bị giá trị mặc định từ dữ liệu ban đầu
  const getInitialValues = () => {
    if (!initialData) return defaultValues;

    // Lấy dữ liệu validation từ configJson
    const validation = initialData.configJson?.validation as Record<string, unknown> | undefined;

    return {
      id: initialData.configId || '', // ✅ Sử dụng configId từ API response
      displayName: initialData.configJson?.displayName as string || initialData.label || '',
      type: initialData.type,
      placeholder: initialData.configJson?.placeholder as string || '',
      defaultValue: initialData.configJson?.defaultValue as string || '',
      description: initialData.configJson?.description as string || '',
      validation: {
        minLength: validation?.minLength ? String(validation.minLength) : '',
        maxLength: validation?.maxLength ? String(validation.maxLength) : '',
        pattern: validation?.pattern ? String(validation.pattern) : '',
      },
      options: initialData.configJson?.options
        ? JSON.stringify(initialData.configJson.options)
        : '',
    };
  };

  return (
    <Card title={initialData ? t('marketing:customField.edit') : t('marketing:customField.add')}>
      <Form
        ref={formRef}
        schema={customFieldSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        defaultValues={getInitialValues()}
        className="p-4 space-y-6"
      >
        {/* Thông tin cơ bản */}
        <div className="space-y-4 mb-6">
          <FormItem
            name="type"
            label={t('marketing:customField.type')}
            required
          >
            <Select
              fullWidth
              options={[
                { value: 'text', label: t('marketing:customField.types.text') },
                { value: 'number', label: t('marketing:customField.types.number') },
                { value: 'boolean', label: t('marketing:customField.types.boolean') },
                { value: 'date', label: t('marketing:customField.types.date') },
                { value: 'select', label: t('marketing:customField.types.select') },
                { value: 'object', label: t('marketing:customField.types.object') },
                { value: 'array', label: t('marketing:customField.types.array') },
              ]}
            />
          </FormItem>

          <FormItem
            name="id"
            label={t('marketing:customField.form.fieldIdLabel')}
            required
          >
            <Input
              fullWidth
              placeholder={t('marketing:customField.form.fieldIdPlaceholder')}
              pattern="^[a-zA-Z0-9_-]+$"
            />
          </FormItem>

          <FormItem
            name="displayName"
            label={t('marketing:customField.form.displayNameLabel')}
            required
          >
            <Input
              fullWidth
              placeholder={t('marketing:customField.form.displayNamePlaceholder')}
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('marketing:customField.form.description')}
          >
            <Textarea
              fullWidth
              rows={3}
              placeholder={t('marketing:customField.form.descriptionPlaceholder')}
            />
          </FormItem>

          {/* Tùy chọn cho kiểu dữ liệu select */}
          <ConditionalField
            condition={{
              field: 'type',
              type: ConditionType.EQUALS,
              value: 'select',
            }}
          >
            <FormItem
              name="options"
              label={t('marketing:customField.form.options')}
            >
              <Textarea
                fullWidth
                rows={6}
                placeholder={t('marketing:customField.form.selectOptionsPlaceholder')}
              />
            </FormItem>
          </ConditionalField>
        </div>

        {/* Checkbox để hiện/ẩn cài đặt nâng cao */}
        <div className="flex items-center space-x-2 mb-4">
          <Checkbox
            checked={showAdvancedSettings}
            onChange={setShowAdvancedSettings}
            label={t('marketing:customField.form.showAdvancedSettings', 'Hiển thị cài đặt nâng cao')}
          />
        </div>

        {/* Cài đặt nâng cao */}
        {showAdvancedSettings && (
          <div className="space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <FormItem
              name="placeholder"
              label={t('marketing:customField.form.placeholder')}
            >
              <Input fullWidth placeholder={t('marketing:customField.form.placeholderPlaceholder')} />
            </FormItem>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'number',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('marketing:customField.form.defaultValue')}
              >
                <Input
                  fullWidth
                  type="number"
                  placeholder={t('marketing:customField.form.defaultValuePlaceholder')}
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'boolean',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('marketing:customField.form.defaultValue')}
              >
                <Select
                  fullWidth
                  placeholder={t('marketing:customField.form.booleanDefaultPlaceholder')}
                  options={[
                    { value: 'true', label: t('marketing:customField.booleanValues.true') },
                    { value: 'false', label: t('marketing:customField.booleanValues.false') },
                  ]}
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.EQUALS,
                value: 'date',
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('marketing:customField.form.defaultValue')}
              >
                <DatePicker
                  fullWidth
                  placeholder={t('marketing:customField.form.dateDefaultPlaceholder')}
                  format="dd/MM/yyyy"
                />
              </FormItem>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'type',
                type: ConditionType.IN,
                value: ['text', 'select', 'object', 'array'],
              }}
            >
              <FormItem
                name="defaultValue"
                label={t('marketing:customField.form.defaultValue')}
              >
                <Input
                  fullWidth
                  placeholder={t('marketing:customField.form.defaultValuePlaceholder')}
                />
              </FormItem>
            </ConditionalField>
          </div>
        )}
      </Form>
    </Card>
  );
};

export default CustomFieldForm;
