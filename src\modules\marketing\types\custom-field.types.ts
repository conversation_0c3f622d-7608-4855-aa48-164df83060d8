/**
 * Types for audience custom field API
 */

import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Custom field type enum
 */
export enum CustomFieldType {
  TEXT = 'string',
  NUMBER = 'number',
  DATE = 'date',
  BOOLEAN = 'boolean',

}

/**
 * Custom field status enum
 */
export enum CustomFieldStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Custom field option
 */
export interface CustomFieldOption {
  id: string;
  label: string;
  value: string;
}

/**
 * Custom field entity
 */
export interface CustomField {
  id: number;
  displayName: string;
  fieldKey: string;
  description?: string;
  dataType: CustomFieldType;
  status?: CustomFieldStatus;
  isRequired?: boolean;
  options?: CustomFieldOption[];
  defaultValue?: string | number | boolean | string[];
  createdAt: string;
  updatedAt: string;

  // Hỗ trợ tương thích ngược với API cũ
  name?: string;
  key?: string;
  type?: CustomFieldType;
}

/**
 * Create custom field request
 */
export interface CreateCustomFieldRequest {
  fieldKey: string;
  displayName: string;
  dataType: CustomFieldType;
  description?: string;
  status?: CustomFieldStatus;
  isRequired?: boolean;
  options?: Omit<CustomFieldOption, 'id'>[];
  defaultValue?: string | number | boolean | string[];

  // Hỗ trợ tương thích ngược với API cũ
  name?: string;
  key?: string;
  type?: CustomFieldType;
}

/**
 * Update custom field request
 */
export interface UpdateCustomFieldRequest {
  fieldKey?: string;
  displayName?: string;
  dataType?: CustomFieldType;
  name?: string;
  description?: string;
  status?: CustomFieldStatus;
  isRequired?: boolean;
  options?: Omit<CustomFieldOption, 'id'>[];
  defaultValue?: string | number | boolean | string[];
}

/**
 * Custom field response
 */
export type CustomFieldResponse = CustomField;

/**
 * Custom field list response
 */
export type CustomFieldListResponse = ApiResponseDto<PaginatedResult<CustomFieldResponse>>;

/**
 * Custom field detail response
 */
export type CustomFieldDetailResponse = ApiResponseDto<CustomFieldResponse>;

/**
 * Custom field query params
 */
export interface CustomFieldQueryParams {
  search?: string;
  type?: CustomFieldType;
  status?: CustomFieldStatus;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: string;
}
