import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Loading } from '@/shared/components/common';
import { useCustomField } from '../../hooks/useCustomFieldQuery';
import { CustomFieldDetail } from '../../services/custom-field.service';
import { CustomField } from '../../types/custom-field.types';
import CustomFieldForm from './CustomFieldForm';

interface CustomFieldDetailFormProps {
  id: number;
  onSubmit: () => void;
  onCancel: () => void;
}

// Helper function to convert CustomField to CustomFieldDetail
const convertToCustomFieldDetail = (customField: CustomField): CustomFieldDetail => {
  return {
    id: customField.id,
    configId: customField.fieldKey,
    label: customField.displayName,
    type: customField.dataType,
    description: customField.description,
    configJson: {
      id: customField.fieldKey,
      label: customField.displayName,
      displayName: customField.displayName,
      type: customField.dataType,
      description: customField.description,
    },
    createdAt: customField.createdAt,
    updatedAt: customField.updatedAt,
  };
};

/**
 * Form chỉnh sửa trường tùy chỉnh với dữ liệu được tải từ API
 */
const CustomFieldDetailForm: React.FC<CustomFieldDetailFormProps> = ({ id, onSubmit, onCancel }) => {
  const { t } = useTranslation(['marketing', 'common']);
  const { data: customFieldData, isLoading, error } = useCustomField(id);

  if (isLoading) {
    return (
      <Card title={t('marketing:customField.edit')}>
        <div className="flex justify-center items-center p-8">
          <Loading size="lg" />
        </div>
      </Card>
    );
  }

  if (error || !customFieldData) {
    return (
      <Card title={t('marketing:customField.edit')}>
        <div className="p-4 text-center text-red-500">
          {t('marketing:customField.loadError')}
        </div>
      </Card>
    );
  }

  // Convert CustomField to CustomFieldDetail
  const customFieldDetail = convertToCustomFieldDetail(customFieldData);

  return (
    <CustomFieldForm
      initialData={customFieldDetail}
      onSubmit={onSubmit}
      onCancel={onCancel}
    />
  );
};

export default CustomFieldDetailForm;
